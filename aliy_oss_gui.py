import os
import sys
import json
import time
import tkinter as tk
from tkinter import ttk, filedialog, messagebox, scrolledtext
from datetime import datetime
import threading
import secrets
import subprocess

# 导入自定义模块（修复路径引用问题）
from aliy_oss_uploader import (
    load_credentials, save_credentials, is_credentials_valid, 
    upload_to_oss, upload_bytes_to_oss, parse_oss_url
)
from refresh_credentials import refresh_and_save_credentials

# 凭证和历史记录文件路径（修改为相对于当前目录的路径）
current_dir = os.path.dirname(os.path.abspath(__file__))
CREDENTIALS_FILE = os.path.join(current_dir, "aliy_credentials.json")
HISTORY_FILE = os.path.join(current_dir, "aliy_upload_history.json")
SETTINGS_FILE = os.path.join(current_dir, "aliy_oss_settings.json")
# 默认的OSS URL示例
DEFAULT_OSS_URL = "https://zm-cloud.oss-cn-beijing.aliyuncs.com/aisou/63750f37593643c7.gif"

class AliyOssUploaderApp:
    def __init__(self, root):
        self.root = root
        self.root.title("阿里云OSS上传工具")
        self.root.geometry("900x700")
        self.root.resizable(True, True)
        
        # 状态变量
        self.is_uploading = False
        self.credentials = None
        self.history_data = []
        
        # 存储配置
        self.bucket_var = tk.StringVar(value="zm-cloud")  # 默认桶名
        self.endpoint_var = tk.StringVar(value="oss-cn-beijing.aliyuncs.com")  # 默认端点
        self.path_prefix_var = tk.StringVar(value="aisou/")  # 默认前缀
        
        # 示例URL变量
        self.example_url_var = tk.StringVar(value=DEFAULT_OSS_URL)
        
        # 文件路径变量
        self.filepath_var = tk.StringVar()
        
        # 上传结果变量
        self.result_url_var = tk.StringVar(value="上传后的URL将显示在这里")
        self.status_var = tk.StringVar(value="就绪")
        self.time_var = tk.StringVar(value="")
        
        # 创建UI界面
        self._create_widgets()
        
        # 加载凭证
        self._load_stored_credentials()
        
        # 加载历史记录
        self._load_history()
        
        # 自动解析示例URL
        self._parse_example_url()

    def _create_widgets(self):
        """创建UI组件"""
        # 创建主框架
        main_frame = ttk.Frame(self.root, padding="10")
        main_frame.pack(fill=tk.BOTH, expand=True)
        
        # 创建选项卡
        tab_control = ttk.Notebook(main_frame)
        
        # 上传选项卡
        upload_tab = ttk.Frame(tab_control)
        tab_control.add(upload_tab, text="文件上传")
        
        # 设置选项卡
        settings_tab = ttk.Frame(tab_control)
        tab_control.add(settings_tab, text="存储设置")
        
        tab_control.pack(expand=1, fill="both")
        
        # ===== 上传选项卡内容 =====
        self._create_upload_tab(upload_tab)
        
        # ===== 设置选项卡内容 =====
        self._create_settings_tab(settings_tab)

    def _create_upload_tab(self, parent):
        """创建上传选项卡内容"""
        # 文件选择区域
        file_frame = ttk.LabelFrame(parent, text="文件选择")
        file_frame.pack(fill=tk.X, padx=5, pady=5)
        
        file_entry = ttk.Entry(file_frame, textvariable=self.filepath_var, width=70)
        file_entry.pack(side=tk.LEFT, fill=tk.X, expand=True, padx=5, pady=5)
        
        browse_button = ttk.Button(file_frame, text="浏览", command=self._browse_file)
        browse_button.pack(side=tk.LEFT, padx=5, pady=5)
        
        # 上传控制区域
        control_frame = ttk.Frame(parent)
        control_frame.pack(fill=tk.X, padx=5, pady=5)
        
        self.upload_button = ttk.Button(control_frame, text="上传文件", command=self._start_upload)
        self.upload_button.pack(side=tk.LEFT, padx=5)
        
        # 凭证状态显示
        self.credential_status_var = tk.StringVar(value="凭证状态: 未知")
        credential_label = ttk.Label(control_frame, textvariable=self.credential_status_var)
        credential_label.pack(side=tk.LEFT, padx=20)
        
        # 目标OSS信息显示区域
        target_frame = ttk.LabelFrame(parent, text="上传目标信息")
        target_frame.pack(fill=tk.X, padx=5, pady=5)
        
        bucket_label = ttk.Label(target_frame, text=f"存储桶: {self.bucket_var.get()}")
        bucket_label.pack(side=tk.LEFT, padx=5, pady=2)
        
        endpoint_label = ttk.Label(target_frame, text=f"节点: {self.endpoint_var.get()}")
        endpoint_label.pack(side=tk.LEFT, padx=20, pady=2)
        
        prefix_label = ttk.Label(target_frame, text=f"前缀: {self.path_prefix_var.get()}")
        prefix_label.pack(side=tk.LEFT, padx=20, pady=2)
        
        # 绑定变量更新事件
        self.bucket_var.trace_add("write", lambda *args: bucket_label.config(text=f"存储桶: {self.bucket_var.get()}"))
        self.endpoint_var.trace_add("write", lambda *args: endpoint_label.config(text=f"节点: {self.endpoint_var.get()}"))
        self.path_prefix_var.trace_add("write", lambda *args: prefix_label.config(text=f"前缀: {self.path_prefix_var.get()}"))
        
        # 进度条区域（虽然现在没用，但保留结构）
        progress_frame = ttk.Frame(parent)
        progress_frame.pack(fill=tk.X, padx=5, pady=5)
        
        ttk.Label(progress_frame, text="状态:").pack(side=tk.LEFT, padx=5)
        ttk.Label(progress_frame, textvariable=self.status_var).pack(side=tk.LEFT, padx=5)
        ttk.Label(progress_frame, textvariable=self.time_var).pack(side=tk.RIGHT, padx=5)
        
        # 上传结果区域
        result_frame = ttk.LabelFrame(parent, text="上传结果")
        result_frame.pack(fill=tk.X, padx=5, pady=5)
        
        result_entry = ttk.Entry(result_frame, textvariable=self.result_url_var, width=80)
        result_entry.pack(side=tk.LEFT, fill=tk.X, expand=True, padx=5, pady=5)
        
        copy_button = ttk.Button(result_frame, text="复制URL", command=self._copy_result_url)
        copy_button.pack(side=tk.LEFT, padx=5, pady=5)
        
        # 日志区域
        log_frame = ttk.LabelFrame(parent, text="上传日志")
        log_frame.pack(fill=tk.BOTH, expand=True, padx=5, pady=5)
        
        self.log_text = scrolledtext.ScrolledText(log_frame, wrap=tk.WORD, width=80, height=8)
        self.log_text.pack(fill=tk.BOTH, expand=True, padx=5, pady=5)
        self.log_text.config(state=tk.DISABLED)
        
        # 历史记录区域
        history_frame = ttk.LabelFrame(parent, text="上传历史 (双击复制URL)")
        history_frame.pack(fill=tk.BOTH, expand=True, padx=5, pady=5)
        
        columns = ("date", "filename", "bucket", "url")
        self.history_tree = ttk.Treeview(history_frame, columns=columns, show="headings", height=6)
        
        self.history_tree.heading("date", text="日期")
        self.history_tree.heading("filename", text="文件名")
        self.history_tree.heading("bucket", text="存储桶")
        self.history_tree.heading("url", text="URL")
        
        self.history_tree.column("date", width=150, anchor=tk.W)
        self.history_tree.column("filename", width=150, anchor=tk.W)
        self.history_tree.column("bucket", width=100, anchor=tk.W)
        self.history_tree.column("url", width=400, anchor=tk.W)
        
        tree_scrollbar = ttk.Scrollbar(history_frame, orient="vertical", command=self.history_tree.yview)
        self.history_tree.configure(yscrollcommand=tree_scrollbar.set)
        
        self.history_tree.grid(row=0, column=0, sticky='nsew')
        tree_scrollbar.grid(row=0, column=1, sticky='ns')
        
        history_frame.grid_rowconfigure(0, weight=1)
        history_frame.grid_columnconfigure(0, weight=1)
        
        self.history_tree.bind("<Double-1>", self._copy_history_url)

    def _create_settings_tab(self, parent):
        """创建设置选项卡内容"""
        # OSS示例URL区域
        example_url_frame = ttk.LabelFrame(parent, text="OSS示例URL (用于提取存储桶和端点)")
        example_url_frame.pack(fill=tk.X, padx=5, pady=5)
        
        ttk.Entry(example_url_frame, textvariable=self.example_url_var, width=80).pack(side=tk.LEFT, fill=tk.X, expand=True, padx=5, pady=5)
        
        parse_button = ttk.Button(example_url_frame, text="解析", command=self._parse_example_url)
        parse_button.pack(side=tk.LEFT, padx=5, pady=5)
        
        # OSS设置区域
        oss_settings_frame = ttk.LabelFrame(parent, text="OSS存储设置 (从示例URL自动提取)")
        oss_settings_frame.pack(fill=tk.X, padx=5, pady=5)
        
        # 存储桶名称
        bucket_frame = ttk.Frame(oss_settings_frame)
        bucket_frame.pack(fill=tk.X, padx=5, pady=2)
        ttk.Label(bucket_frame, text="存储桶名称:", width=12).pack(side=tk.LEFT)
        ttk.Entry(bucket_frame, textvariable=self.bucket_var, width=30).pack(side=tk.LEFT, fill=tk.X, expand=True)
        
        # Endpoint
        endpoint_frame = ttk.Frame(oss_settings_frame)
        endpoint_frame.pack(fill=tk.X, padx=5, pady=2)
        ttk.Label(endpoint_frame, text="Endpoint:", width=12).pack(side=tk.LEFT)
        ttk.Entry(endpoint_frame, textvariable=self.endpoint_var, width=30).pack(side=tk.LEFT, fill=tk.X, expand=True)
        
        # 路径前缀
        path_frame = ttk.Frame(oss_settings_frame)
        path_frame.pack(fill=tk.X, padx=5, pady=2)
        ttk.Label(path_frame, text="路径前缀:", width=12).pack(side=tk.LEFT)
        ttk.Entry(path_frame, textvariable=self.path_prefix_var, width=30).pack(side=tk.LEFT, fill=tk.X, expand=True)
        
        # 保存设置按钮
        settings_btn_frame = ttk.Frame(parent)
        settings_btn_frame.pack(fill=tk.X, padx=5, pady=10)
        
        save_settings_button = ttk.Button(settings_btn_frame, text="保存设置", command=self._save_oss_settings)
        save_settings_button.pack(side=tk.LEFT, padx=5)
        
        # 解析结果显示区域
        parse_result_frame = ttk.LabelFrame(parent, text="URL解析结果")
        parse_result_frame.pack(fill=tk.X, padx=5, pady=5)
        
        self.parse_result_text = scrolledtext.ScrolledText(parse_result_frame, wrap=tk.WORD, width=80, height=5)
        self.parse_result_text.pack(fill=tk.BOTH, expand=True)
        self.parse_result_text.config(state=tk.DISABLED)
         
    def _parse_example_url(self):
        """解析示例URL，提取存储桶和端点信息"""
        url = self.example_url_var.get()
        if not url:
            messagebox.showinfo("提示", "请输入OSS URL进行解析")
            return
            
        result = parse_oss_url(url)
        self.parse_result_text.config(state=tk.NORMAL)
        self.parse_result_text.delete(1.0, tk.END)
        
        if result:
            self.bucket_var.set(result['bucket'])
            self.endpoint_var.set(result['endpoint'])
            
            # 提取前缀
            object_path = result['object']
            if '/' in object_path:
                prefix = object_path.rsplit('/', 1)[0] + '/'
                self.path_prefix_var.set(prefix)
                
            self.parse_result_text.insert(tk.END, f"存储桶(Bucket): {result['bucket']}\n")
            self.parse_result_text.insert(tk.END, f"终端节点(Endpoint): {result['endpoint']}\n")
            self.parse_result_text.insert(tk.END, f"对象路径(Object): {result['object']}\n")
            self.parse_result_text.insert(tk.END, f"提取的前缀: {self.path_prefix_var.get()}\n")
            
            self._log(f"已从URL自动提取存储设置: {result['bucket']}.{result['endpoint']}")
        else:
            self.parse_result_text.insert(tk.END, "无法解析URL，请检查格式是否正确")
            
        self.parse_result_text.config(state=tk.DISABLED)
        
    def _load_stored_credentials(self):
        """加载存储的凭证"""
        self.credentials = load_credentials()
        
        # 更新凭证状态显示
        if self.credentials and is_credentials_valid(self.credentials):
            # 尝试从request_log.json获取最后使用的配置ID
            config_id = self._get_last_config_id()
            self.credential_status_var.set(f"凭证状态: 有效 (配置 #{config_id})")
        else:
            self.credential_status_var.set("凭证状态: 无效或过期")
            
    def _get_last_config_id(self):
        """从request_log.json获取最后使用的配置ID"""
        try:
            request_log_path = os.path.join(os.path.dirname(os.path.abspath(__file__)), "request_log.json")
            if os.path.exists(request_log_path):
                with open(request_log_path, 'r') as f:
                    log_data = json.load(f)
                    # 配置ID在文件中是0-4，显示时转为1-5
                    return log_data.get("last_config_id", -1) + 1
        except Exception:
            pass
        return "未知"
        
    def _save_oss_settings(self):
        """保存OSS设置"""
        settings = {
            "bucket": self.bucket_var.get(),
            "endpoint": self.endpoint_var.get(),
            "path_prefix": self.path_prefix_var.get(),
            "example_url": self.example_url_var.get()
        }
        
        if not settings["bucket"] or not settings["endpoint"]:
            messagebox.showerror("错误", "请填写存储桶名称和Endpoint")
            return
            
        try:
            with open(SETTINGS_FILE, 'w') as f:
                json.dump(settings, f, indent=2)
            self._log("OSS设置已保存")
        except Exception as e:
            messagebox.showerror("保存失败", f"保存设置时发生错误: {str(e)}")
            
    def _load_oss_settings(self):
        """加载OSS设置"""
        try:
            if os.path.exists(SETTINGS_FILE):
                with open(SETTINGS_FILE, 'r') as f:
                    settings = json.load(f)
                    
                self.bucket_var.set(settings.get("bucket", ""))
                self.endpoint_var.set(settings.get("endpoint", ""))
                self.path_prefix_var.set(settings.get("path_prefix", "uploads/"))
                
                # 如果有示例URL也加载它
                if "example_url" in settings:
                    self.example_url_var.set(settings.get("example_url"))
        except Exception as e:
            self._log(f"加载OSS设置失败: {str(e)}")
             
    def _browse_file(self):
        """浏览选择文件"""
        filepath = filedialog.askopenfilename()
        if filepath:
            self.filepath_var.set(filepath)
            
    def _start_upload(self):
        """开始上传文件"""
        if self.is_uploading:
            messagebox.showinfo("提示", "正在上传中，请等待")
            return
            
        filepath = self.filepath_var.get()
        if not filepath:
            messagebox.showinfo("提示", "请选择要上传的文件")
            return
            
        if not os.path.exists(filepath):
            messagebox.showerror("错误", f"文件不存在: {filepath}")
            return
            
        bucket = self.bucket_var.get()
        endpoint = self.endpoint_var.get()
        
        if not bucket or not endpoint:
            messagebox.showerror("错误", "请在设置选项卡中填写存储桶和Endpoint信息")
            return
                
        # 在新线程中执行上传
        threading.Thread(target=self._perform_upload, args=(filepath,), daemon=True).start()
            
    def _perform_upload(self, filepath):
        """执行上传操作"""
        try:
            self.is_uploading = True
            self.upload_button.config(state=tk.DISABLED)
            
            filename = os.path.basename(filepath)
            start_time = time.time()
            
            # 创建对象名称，使用20位十六进制哈希值作为文件名
            path_prefix = self.path_prefix_var.get() or "aisou/"
            if not path_prefix.endswith('/'):
                path_prefix += '/'
                
            # 生成文件哈希名称 (格式： ebebc0aa03dd88cb1c7f.png)
            file_ext = os.path.splitext(filename)[1]  # 获取文件扩展名
            rand_hex = self._generate_random_hex(20)  # 生成20位随机十六进制
            object_name = f"{path_prefix}{rand_hex}{file_ext}"
            
            self._log(f"开始上传: {filename} -> {object_name}")
            self.status_var.set("上传中...")
            self.root.update_idletasks()
            
            bucket = self.bucket_var.get()
            endpoint = self.endpoint_var.get()
            
            # 上传文件，最多尝试3次
            max_retries = 3
            retry_count = 0
            result = None
            
            while retry_count < max_retries:
                # 检查凭证是否有效，如果无效则刷新
                if not self.credentials or not is_credentials_valid(self.credentials):
                    self._log("凭证无效或过期，正在自动刷新...")
                    # 自动轮换配置获取凭证
                    self.credentials = refresh_and_save_credentials(None)
                    if not self.credentials:
                        self._log("凭证刷新失败，无法继续上传")
                        self.status_var.set("凭证刷新失败")
                        messagebox.showerror("上传失败", "凭证刷新失败，无法继续上传")
                        break
                    else:
                        config_id = self._get_last_config_id()
                        self.credential_status_var.set(f"凭证状态: 有效 (配置 #{config_id})")
                
                # 尝试上传
                result = upload_to_oss(
                    file_path=filepath, 
                    object_name=object_name,
                    credentials=self.credentials,
                    bucket_name=bucket,
                    endpoint=endpoint
                )
                
                if result['success']:
                    # 上传成功，跳出循环
                    break
                else:
                    retry_count += 1
                    if retry_count < max_retries:
                        self._log(f"上传失败: {result['error']}，正在尝试刷新凭证并重试 ({retry_count}/{max_retries})")
                        # 使用不同配置刷新凭证 (轮换配置)
                        config_id = retry_count % 5 + 1  # 1-5之间轮换
                        self._log(f"使用配置 #{config_id} 刷新凭证...")
                        self.credentials = refresh_and_save_credentials(config_id)
                        self.credential_status_var.set(f"凭证状态: 有效 (配置 #{config_id})")
                    else:
                        self._log(f"上传失败: {result['error']}，已达到最大重试次数")
            
            end_time = time.time()
            elapsed = round(end_time - start_time, 2)
            self.time_var.set(f"耗时: {elapsed}秒")
            
            if result and result['success']:
                self._log(f"上传成功: {result['url']}")
                self.status_var.set("上传成功")
                self.result_url_var.set(result['url'])
                
                # 添加到历史记录
                self._add_to_history(filename, bucket, result['url'])
                
                # 自动复制URL到剪贴板
                self.root.clipboard_clear()
                self.root.clipboard_append(result['url'])
                self._log("URL已复制到剪贴板")
            else:
                error_msg = result['error'] if result else "未知错误"
                self._log(f"上传失败: {error_msg}")
                self.status_var.set("上传失败")
                messagebox.showerror("上传失败", error_msg)
        except Exception as e:
            self._log(f"上传过程发生错误: {str(e)}")
            self.status_var.set("上传出错")
            messagebox.showerror("上传错误", str(e))
            
        finally:
            self.is_uploading = False
            self.upload_button.config(state=tk.NORMAL)
            
    def _generate_random_hex(self, length=20):
        """生成指定长度的随机十六进制字符串"""
        # 使用secrets模块生成更安全的随机数
        random_bytes = secrets.token_bytes(length // 2 + 1)
        # 转换为十六进制字符串并截取指定长度
        hex_string = random_bytes.hex()[:length]
        return hex_string
        
    def _copy_result_url(self):
        """复制上传结果URL"""
        url = self.result_url_var.get()
        if url and url != "上传后的URL将显示在这里":
            self.root.clipboard_clear()
            self.root.clipboard_append(url)
            self.status_var.set("URL已复制到剪贴板")
            
    def _add_to_history(self, filename, bucket, url):
        """添加记录到上传历史"""
        now = datetime.now().strftime("%Y-%m-%d %H:%M:%S")
        
        # 添加到内存中的历史记录
        self.history_data.append({
            "date": now,
            "filename": filename,
            "bucket": bucket,
            "url": url
        })
        
        # 更新历史记录显示
        self._populate_history_tree()
        
        # 保存历史记录到文件
        self._save_history()
        
    def _load_history(self):
        """加载历史记录"""
        try:
            if os.path.exists(HISTORY_FILE):
                with open(HISTORY_FILE, 'r') as f:
                    self.history_data = json.load(f)
                self._populate_history_tree()
        except Exception as e:
            self._log(f"加载历史记录失败: {str(e)}")
            
    def _save_history(self):
        """保存历史记录到文件"""
        try:
            # 保留最近100条记录
            if len(self.history_data) > 100:
                self.history_data = self.history_data[-100:]
                
            with open(HISTORY_FILE, 'w') as f:
                json.dump(self.history_data, f, indent=2)
        except Exception as e:
            self._log(f"保存历史记录失败: {str(e)}")
            
    def _populate_history_tree(self):
        """更新历史记录到树视图"""
        # 清空现有条目
        for item in self.history_tree.get_children():
            self.history_tree.delete(item)
            
        # 添加历史记录，最新的在最前面
        for item in reversed(self.history_data):
            self.history_tree.insert(
                "", 
                0, 
                values=(
                    item.get("date", ""),
                    item.get("filename", ""),
                    item.get("bucket", ""),
                    item.get("url", "")
                )
            )
            
    def _copy_history_url(self, event=None):
        """双击复制历史记录中的URL"""
        selection = self.history_tree.selection()
        if selection:
            item = self.history_tree.item(selection[0])
            url = item['values'][3]  # URL在第4列
            
            if url:
                self.root.clipboard_clear()
                self.root.clipboard_append(url)
                self.status_var.set("历史URL已复制到剪贴板")
            
    def _log(self, message):
        """添加日志消息"""
        timestamp = datetime.now().strftime("%H:%M:%S")
        log_message = f"[{timestamp}] {message}\n"
        
        self.log_text.config(state=tk.NORMAL)
        self.log_text.insert(tk.END, log_message)
        self.log_text.see(tk.END)
        self.log_text.config(state=tk.DISABLED)
        self.root.update_idletasks()

if __name__ == "__main__":
    root = tk.Tk()
    app = AliyOssUploaderApp(root)
    # 加载设置
    app._load_oss_settings()
    root.mainloop() 