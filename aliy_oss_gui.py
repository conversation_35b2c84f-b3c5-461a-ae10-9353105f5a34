import os
import sys
import json
import time
import tkinter as tk
from tkinter import ttk, filedialog, messagebox, scrolledtext
from datetime import datetime
import threading
import secrets
import subprocess

# 导入自定义模块
from aliy_oss_uploader import upload_to_oss, upload_bytes_to_oss, parse_oss_url
from credential_manager import (
    load_credentials, save_credentials, is_credentials_valid,
    refresh_and_save_credentials, credential_manager
)

# 文件路径
current_dir = os.path.dirname(os.path.abspath(__file__))
CREDENTIALS_FILE = os.path.join(current_dir, "aliy_credentials.json")
HISTORY_FILE = os.path.join(current_dir, "aliy_upload_history.json")
SETTINGS_FILE = os.path.join(current_dir, "aliy_oss_settings.json")

class AliyOssUploaderApp:
    def __init__(self, root):
        self.root = root
        self.root.title("阿里云OSS上传工具 - 简化版")
        self.root.geometry("800x600")
        self.root.resizable(True, True)

        # 状态变量
        self.is_uploading = False
        self.credentials = None
        self.history_data = []

        # OSS配置变量
        self.bucket_var = tk.StringVar(value="zm-cloud")
        self.endpoint_var = tk.StringVar(value="oss-cn-beijing.aliyuncs.com")
        self.path_prefix_var = tk.StringVar(value="aisou/")

        # 凭证输入变量
        self.manual_access_key_var = tk.StringVar()
        self.manual_secret_key_var = tk.StringVar()
        self.manual_token_var = tk.StringVar()
        self.credential_mode_var = tk.StringVar(value="auto")  # auto 或 manual

        # 界面变量
        self.filepath_var = tk.StringVar()
        self.result_url_var = tk.StringVar(value="上传后的URL将显示在这里")
        self.status_var = tk.StringVar(value="就绪")

        # 创建UI界面
        self._create_widgets()

        # 加载设置和凭证
        self._load_settings()
        self._load_stored_credentials()
        self._load_history()

    def _create_widgets(self):
        """创建简化的UI组件"""
        # 创建主框架
        main_frame = ttk.Frame(self.root, padding="10")
        main_frame.pack(fill=tk.BOTH, expand=True)

        # 1. OSS配置区域
        self._create_oss_config_section(main_frame)

        # 2. 凭证配置区域
        self._create_credential_section(main_frame)

        # 3. 文件上传区域
        self._create_upload_section(main_frame)

        # 4. 结果显示区域
        self._create_result_section(main_frame)

        # 5. 历史记录区域（可折叠）
        self._create_history_section(main_frame)

    def _create_oss_config_section(self, parent):
        """创建OSS配置区域"""
        config_frame = ttk.LabelFrame(parent, text="OSS存储配置")
        config_frame.pack(fill=tk.X, padx=5, pady=5)

        # 第一行：存储桶和端点
        row1 = ttk.Frame(config_frame)
        row1.pack(fill=tk.X, padx=5, pady=2)

        ttk.Label(row1, text="存储桶:").pack(side=tk.LEFT)
        ttk.Entry(row1, textvariable=self.bucket_var, width=20).pack(side=tk.LEFT, padx=(5,15))

        ttk.Label(row1, text="端点:").pack(side=tk.LEFT)
        ttk.Entry(row1, textvariable=self.endpoint_var, width=30).pack(side=tk.LEFT, padx=(5,15))

        ttk.Label(row1, text="前缀:").pack(side=tk.LEFT)
        ttk.Entry(row1, textvariable=self.path_prefix_var, width=15).pack(side=tk.LEFT, padx=5)

        # 保存配置按钮
        ttk.Button(row1, text="保存配置", command=self._save_settings).pack(side=tk.RIGHT, padx=5)

    def _create_credential_section(self, parent):
        """创建凭证配置区域"""
        cred_frame = ttk.LabelFrame(parent, text="凭证配置")
        cred_frame.pack(fill=tk.X, padx=5, pady=5)

        # 凭证模式选择
        mode_frame = ttk.Frame(cred_frame)
        mode_frame.pack(fill=tk.X, padx=5, pady=2)

        ttk.Radiobutton(mode_frame, text="自动获取凭证", variable=self.credential_mode_var,
                       value="auto", command=self._on_credential_mode_change).pack(side=tk.LEFT)
        ttk.Radiobutton(mode_frame, text="手动输入凭证", variable=self.credential_mode_var,
                       value="manual", command=self._on_credential_mode_change).pack(side=tk.LEFT, padx=20)

        # 凭证状态显示
        self.credential_status_var = tk.StringVar(value="凭证状态: 未知")
        ttk.Label(mode_frame, textvariable=self.credential_status_var).pack(side=tk.RIGHT)

        # 手动凭证输入区域（默认隐藏）
        self.manual_cred_frame = ttk.Frame(cred_frame)

        # Access Key ID
        ak_frame = ttk.Frame(self.manual_cred_frame)
        ak_frame.pack(fill=tk.X, padx=5, pady=2)
        ttk.Label(ak_frame, text="Access Key ID:", width=15).pack(side=tk.LEFT)
        ttk.Entry(ak_frame, textvariable=self.manual_access_key_var, width=40).pack(side=tk.LEFT, padx=5)

        # Access Key Secret
        sk_frame = ttk.Frame(self.manual_cred_frame)
        sk_frame.pack(fill=tk.X, padx=5, pady=2)
        ttk.Label(sk_frame, text="Access Key Secret:", width=15).pack(side=tk.LEFT)
        ttk.Entry(sk_frame, textvariable=self.manual_secret_key_var, width=40, show="*").pack(side=tk.LEFT, padx=5)

        # Security Token
        token_frame = ttk.Frame(self.manual_cred_frame)
        token_frame.pack(fill=tk.X, padx=5, pady=2)
        ttk.Label(token_frame, text="Security Token:", width=15).pack(side=tk.LEFT)
        ttk.Entry(token_frame, textvariable=self.manual_token_var, width=40, show="*").pack(side=tk.LEFT, padx=5)

        # 保存手动凭证按钮
        ttk.Button(self.manual_cred_frame, text="保存凭证", command=self._save_manual_credentials).pack(pady=5)

    def _create_upload_section(self, parent):
        """创建文件上传区域"""
        upload_frame = ttk.LabelFrame(parent, text="文件上传")
        upload_frame.pack(fill=tk.X, padx=5, pady=5)

        # 文件选择
        file_frame = ttk.Frame(upload_frame)
        file_frame.pack(fill=tk.X, padx=5, pady=5)

        ttk.Entry(file_frame, textvariable=self.filepath_var).pack(side=tk.LEFT, fill=tk.X, expand=True, padx=(0,5))
        ttk.Button(file_frame, text="选择文件", command=self._browse_file).pack(side=tk.LEFT, padx=5)
        self.upload_button = ttk.Button(file_frame, text="上传", command=self._start_upload)
        self.upload_button.pack(side=tk.LEFT, padx=5)

        # 状态显示
        status_frame = ttk.Frame(upload_frame)
        status_frame.pack(fill=tk.X, padx=5, pady=2)
        ttk.Label(status_frame, text="状态:").pack(side=tk.LEFT)
        ttk.Label(status_frame, textvariable=self.status_var).pack(side=tk.LEFT, padx=5)

    def _create_result_section(self, parent):
        """创建结果显示区域"""
        result_frame = ttk.LabelFrame(parent, text="上传结果")
        result_frame.pack(fill=tk.X, padx=5, pady=5)

        url_frame = ttk.Frame(result_frame)
        url_frame.pack(fill=tk.X, padx=5, pady=5)

        ttk.Entry(url_frame, textvariable=self.result_url_var).pack(side=tk.LEFT, fill=tk.X, expand=True, padx=(0,5))
        ttk.Button(url_frame, text="复制", command=self._copy_result_url).pack(side=tk.LEFT)

    def _create_history_section(self, parent):
        """创建历史记录区域"""
        history_frame = ttk.LabelFrame(parent, text="上传历史 (双击复制URL)")
        history_frame.pack(fill=tk.BOTH, expand=True, padx=5, pady=5)

        # 创建树形视图
        columns = ("date", "filename", "url")
        self.history_tree = ttk.Treeview(history_frame, columns=columns, show="headings", height=6)

        self.history_tree.heading("date", text="日期")
        self.history_tree.heading("filename", text="文件名")
        self.history_tree.heading("url", text="URL")

        self.history_tree.column("date", width=150)
        self.history_tree.column("filename", width=200)
        self.history_tree.column("url", width=400)

        # 滚动条
        scrollbar = ttk.Scrollbar(history_frame, orient="vertical", command=self.history_tree.yview)
        self.history_tree.configure(yscrollcommand=scrollbar.set)

        self.history_tree.pack(side=tk.LEFT, fill=tk.BOTH, expand=True)
        scrollbar.pack(side=tk.RIGHT, fill=tk.Y)

        self.history_tree.bind("<Double-1>", self._copy_history_url)

    def _on_credential_mode_change(self):
        """凭证模式切换处理"""
        if self.credential_mode_var.get() == "manual":
            self.manual_cred_frame.pack(fill=tk.X, padx=5, pady=5)
        else:
            self.manual_cred_frame.pack_forget()

    def _save_manual_credentials(self):
        """保存手动输入的凭证"""
        access_key = self.manual_access_key_var.get().strip()
        secret_key = self.manual_secret_key_var.get().strip()
        token = self.manual_token_var.get().strip()

        if not all([access_key, secret_key, token]):
            messagebox.showerror("错误", "请填写完整的凭证信息")
            return

        # 创建凭证字典
        credentials = {
            "access_key_id": access_key,
            "access_key_secret": secret_key,
            "security_token": token,
            "expiration": "手动输入",
            "source": "手动输入"
        }

        try:
            save_credentials(credentials)
            self.credentials = credentials
            self._update_credential_status()
            messagebox.showinfo("成功", "凭证已保存")
        except Exception as e:
            messagebox.showerror("错误", f"保存凭证失败: {str(e)}")

    def _save_settings(self):
        """保存设置"""
        settings = {
            "bucket": self.bucket_var.get(),
            "endpoint": self.endpoint_var.get(),
            "path_prefix": self.path_prefix_var.get(),
            "credential_mode": self.credential_mode_var.get()
        }

        if not settings["bucket"] or not settings["endpoint"]:
            messagebox.showerror("错误", "请填写存储桶名称和端点")
            return

        try:
            with open(SETTINGS_FILE, 'w', encoding='utf-8') as f:
                json.dump(settings, f, indent=2, ensure_ascii=False)
            self.status_var.set("设置已保存")
        except Exception as e:
            messagebox.showerror("保存失败", f"保存设置时发生错误: {str(e)}")

    def _load_settings(self):
        """加载设置"""
        try:
            if os.path.exists(SETTINGS_FILE):
                with open(SETTINGS_FILE, 'r', encoding='utf-8') as f:
                    settings = json.load(f)

                self.bucket_var.set(settings.get("bucket", "zm-cloud"))
                self.endpoint_var.set(settings.get("endpoint", "oss-cn-beijing.aliyuncs.com"))
                self.path_prefix_var.set(settings.get("path_prefix", "aisou/"))
                self.credential_mode_var.set(settings.get("credential_mode", "auto"))

                # 触发凭证模式切换
                self._on_credential_mode_change()
        except Exception as e:
            print(f"加载设置失败: {str(e)}")
        
    def _load_stored_credentials(self):
        """加载存储的凭证"""
        self.credentials = load_credentials()
        self._update_credential_status()

    def _update_credential_status(self):
        """更新凭证状态显示"""
        if self.credentials and is_credentials_valid(self.credentials):
            source = self.credentials.get("source", "未知来源")
            self.credential_status_var.set(f"凭证状态: 有效 ({source})")
        else:
            self.credential_status_var.set("凭证状态: 无效或过期")
        

             
    def _browse_file(self):
        """浏览选择文件"""
        filepath = filedialog.askopenfilename()
        if filepath:
            self.filepath_var.set(filepath)
            
    def _start_upload(self):
        """开始上传文件"""
        if self.is_uploading:
            messagebox.showinfo("提示", "正在上传中，请等待")
            return
            
        filepath = self.filepath_var.get()
        if not filepath:
            messagebox.showinfo("提示", "请选择要上传的文件")
            return
            
        if not os.path.exists(filepath):
            messagebox.showerror("错误", f"文件不存在: {filepath}")
            return
            
        bucket = self.bucket_var.get()
        endpoint = self.endpoint_var.get()
        
        if not bucket or not endpoint:
            messagebox.showerror("错误", "请填写存储桶和端点信息")
            return
                
        # 在新线程中执行上传
        threading.Thread(target=self._perform_upload, args=(filepath,), daemon=True).start()
            
    def _perform_upload(self, filepath):
        """执行上传操作"""
        try:
            self.is_uploading = True
            self.upload_button.config(state=tk.DISABLED)
            
            filename = os.path.basename(filepath)
            start_time = time.time()
            
            # 创建对象名称，使用20位十六进制哈希值作为文件名
            path_prefix = self.path_prefix_var.get() or "aisou/"
            if not path_prefix.endswith('/'):
                path_prefix += '/'
                
            # 生成文件哈希名称 (格式： ebebc0aa03dd88cb1c7f.png)
            file_ext = os.path.splitext(filename)[1]  # 获取文件扩展名
            rand_hex = self._generate_random_hex(20)  # 生成20位随机十六进制
            object_name = f"{path_prefix}{rand_hex}{file_ext}"
            
            self.status_var.set("上传中...")
            self.root.update_idletasks()
            
            bucket = self.bucket_var.get()
            endpoint = self.endpoint_var.get()
            
            # 上传文件，最多尝试3次
            max_retries = 3
            retry_count = 0
            result = None
            
            while retry_count < max_retries:
                # 检查凭证是否有效
                if not self.credentials or not is_credentials_valid(self.credentials):
                    if self.credential_mode_var.get() == "manual":
                        # 手动模式下，提示用户检查凭证
                        messagebox.showerror("凭证错误", "手动输入的凭证无效或已过期，请重新输入")
                        break
                    else:
                        # 自动模式下，尝试刷新凭证
                        self.status_var.set("正在刷新凭证...")
                        self.credentials = credential_manager.get_valid_credentials()
                        if not self.credentials:
                            self.status_var.set("凭证刷新失败")
                            messagebox.showerror("上传失败", "凭证刷新失败，无法继续上传")
                            break
                        else:
                            self._update_credential_status()
                
                # 尝试上传
                result = upload_to_oss(
                    file_path=filepath, 
                    object_name=object_name,
                    credentials=self.credentials,
                    bucket_name=bucket,
                    endpoint=endpoint
                )
                
                if result['success']:
                    # 上传成功，跳出循环
                    break
                else:
                    retry_count += 1
                    if retry_count < max_retries:
                        if self.credential_mode_var.get() == "manual":
                            # 手动模式下不重试，直接失败
                            self.status_var.set(f"上传失败: {result['error']}")
                            break
                        else:
                            # 自动模式下尝试刷新凭证重试
                            self.status_var.set(f"重试中 ({retry_count}/{max_retries})...")
                            config_id = retry_count % 3  # 0-2之间轮换（对应3个配置）
                            self.credentials = credential_manager.refresh_credentials(config_id)
                            if self.credentials:
                                self._update_credential_status()
                    else:
                        self.status_var.set(f"上传失败: {result['error']}")
            
            if result and result['success']:
                self.status_var.set("上传成功")
                self.result_url_var.set(result['url'])

                # 添加到历史记录
                self._add_to_history(filename, result['url'])

                # 自动复制URL到剪贴板
                self.root.clipboard_clear()
                self.root.clipboard_append(result['url'])
                self.status_var.set("上传成功，URL已复制到剪贴板")
            else:
                error_msg = result['error'] if result else "未知错误"
                self.status_var.set(f"上传失败: {error_msg}")
                messagebox.showerror("上传失败", error_msg)
        except Exception as e:
            self.status_var.set(f"上传出错: {str(e)}")
            messagebox.showerror("上传错误", str(e))

        finally:
            self.is_uploading = False
            self.upload_button.config(state=tk.NORMAL)
            
    def _generate_random_hex(self, length=20):
        """生成指定长度的随机十六进制字符串"""
        # 使用secrets模块生成更安全的随机数
        random_bytes = secrets.token_bytes(length // 2 + 1)
        # 转换为十六进制字符串并截取指定长度
        hex_string = random_bytes.hex()[:length]
        return hex_string
        
    def _copy_result_url(self):
        """复制上传结果URL"""
        url = self.result_url_var.get()
        if url and url != "上传后的URL将显示在这里":
            self.root.clipboard_clear()
            self.root.clipboard_append(url)
            self.status_var.set("URL已复制到剪贴板")
            
    def _add_to_history(self, filename, url):
        """添加记录到上传历史"""
        now = datetime.now().strftime("%Y-%m-%d %H:%M:%S")

        # 添加到内存中的历史记录
        self.history_data.append({
            "date": now,
            "filename": filename,
            "url": url
        })

        # 更新历史记录显示
        self._populate_history_tree()

        # 保存历史记录到文件
        self._save_history()
        
    def _load_history(self):
        """加载历史记录"""
        try:
            if os.path.exists(HISTORY_FILE):
                with open(HISTORY_FILE, 'r') as f:
                    self.history_data = json.load(f)
                self._populate_history_tree()
        except Exception as e:
            print(f"加载历史记录失败: {str(e)}")

    def _save_history(self):
        """保存历史记录到文件"""
        try:
            # 保留最近100条记录
            if len(self.history_data) > 100:
                self.history_data = self.history_data[-100:]

            with open(HISTORY_FILE, 'w', encoding='utf-8') as f:
                json.dump(self.history_data, f, indent=2, ensure_ascii=False)
        except Exception as e:
            print(f"保存历史记录失败: {str(e)}")
            
    def _populate_history_tree(self):
        """更新历史记录到树视图"""
        # 清空现有条目
        for item in self.history_tree.get_children():
            self.history_tree.delete(item)

        # 添加历史记录，最新的在最前面
        for item in reversed(self.history_data):
            self.history_tree.insert(
                "",
                0,
                values=(
                    item.get("date", ""),
                    item.get("filename", ""),
                    item.get("url", "")
                )
            )
            
    def _copy_history_url(self, event=None):
        """双击复制历史记录中的URL"""
        selection = self.history_tree.selection()
        if selection:
            item = self.history_tree.item(selection[0])
            url = item['values'][2]  # URL在第3列

            if url:
                self.root.clipboard_clear()
                self.root.clipboard_append(url)
                self.status_var.set("历史URL已复制到剪贴板")
            
if __name__ == "__main__":
    root = tk.Tk()
    app = AliyOssUploaderApp(root)
    root.mainloop()