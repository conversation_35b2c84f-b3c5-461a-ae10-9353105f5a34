"""
简化的凭证管理模块
整合了原来的 refresh_credentials.py 和 assume_role.py 的功能
"""
import os
import json
import requests
import random
from datetime import datetime, timedelta

# 文件路径
SCRIPT_DIR = os.path.dirname(os.path.abspath(__file__))
CREDENTIALS_FILE = os.path.join(SCRIPT_DIR, "aliy_credentials.json")
CONFIG_FILE = os.path.join(SCRIPT_DIR, "credential_config.json")

# API配置
API_URL = "https://bot.n.cn/api/v1/aliy/assumerole"

# 简化的请求头配置（使用原来的有效配置）
HEADER_CONFIGS = [
    {
        "name": "配置1",
        "headers": {
            "access-token": "48239925286233875932760370017484",
            "device-platform": "Web",
            "origin": "https://bot.n.cn",
            "referer": "https://bot.n.cn/?src=dh_bj",
            "timestamp": "2025-05-28T17:08:20+08:00",
            "user-agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********",
            "zm-token": "76f6255d73d1d73487297e6a7c8cecbe",
            "zm-ua": "99d149899c4f2f3d79df1f8e73f539ef",
            "zm-ver": "1.2"
        }
    },
    {
        "name": "配置2",
        "headers": {
            "access-token": "48239925216757509597943900017484",
            "device-platform": "Web",
            "origin": "https://bot.n.cn",
            "referer": "https://bot.n.cn/?src=dh_2nd",
            "timestamp": "2025-05-28T17:14:52+08:00",
            "user-agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/136.0.0.0 Safari/537.36",
            "zm-token": "d8feab78effb765425ba672cdba04f1f",
            "zm-ua": "0e0369e2813db7deb26e5937c353aab4",
            "zm-ver": "1.2"
        }
    },
    {
        "name": "配置3",
        "headers": {
            "access-token": "25170478133103499604803100017484",
            "device-platform": "Web",
            "origin": "https://bot.n.cn",
            "referer": "https://bot.n.cn/?src=dh_3rd",
            "timestamp": "2025-05-28T17:24:48+08:00",
            "user-agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Firefox/123.0",
            "zm-token": "8f44b6af76fbe4169cda02009c104f08",
            "zm-ua": "0e0369e2813db7deb26e5937c353aab4",
            "zm-ver": "1.2"
        }
    }
]

class CredentialManager:
    """简化的凭证管理器"""
    
    def __init__(self):
        self.config = self._load_config()
    
    def _load_config(self):
        """加载配置"""
        default_config = {
            "last_used_config": 0,
            "success_count": [0] * len(HEADER_CONFIGS),
            "last_success_time": None
        }
        
        try:
            if os.path.exists(CONFIG_FILE):
                with open(CONFIG_FILE, 'r', encoding='utf-8') as f:
                    config = json.load(f)
                    # 确保配置完整
                    for key in default_config:
                        if key not in config:
                            config[key] = default_config[key]
                    return config
        except Exception:
            pass
        
        return default_config
    
    def _save_config(self):
        """保存配置"""
        try:
            with open(CONFIG_FILE, 'w', encoding='utf-8') as f:
                json.dump(self.config, f, indent=2, ensure_ascii=False)
        except Exception as e:
            print(f"保存配置失败: {e}")
    
    def _select_best_config(self):
        """选择最佳配置"""
        # 简单策略：轮换使用，优先选择成功率高的
        success_rates = []
        for i, count in enumerate(self.config["success_count"]):
            total_attempts = max(count + 1, 1)  # 避免除零
            success_rates.append((count / total_attempts, i))
        
        # 按成功率排序，选择最高的
        success_rates.sort(reverse=True)
        return success_rates[0][1]
    
    def get_credentials_from_api(self, config_id=None):
        """从API获取凭证"""
        if config_id is None:
            config_id = self._select_best_config()
        elif config_id < 0 or config_id >= len(HEADER_CONFIGS):
            config_id = 0
        
        config = HEADER_CONFIGS[config_id]
        
        try:
            response = requests.post(API_URL, headers=config["headers"], timeout=10)
            response.raise_for_status()
            
            data = response.json()
            if data.get("code") == 0 and "data" in data:
                credentials = data["data"]
                
                # 更新成功统计
                self.config["success_count"][config_id] += 1
                self.config["last_used_config"] = config_id
                self.config["last_success_time"] = datetime.now().isoformat()
                self._save_config()
                
                return {
                    "access_key_id": credentials.get("access_key_id"),
                    "access_key_secret": credentials.get("access_key_secret"),
                    "security_token": credentials.get("security_token"),
                    "expiration": credentials.get("expiration"),
                    "source": f"API-{config['name']}"
                }
            else:
                print(f"API返回错误: {data.get('msg', '未知错误')}")
                return None
                
        except requests.RequestException as e:
            print(f"请求失败: {e}")
            return None
        except Exception as e:
            print(f"获取凭证时发生错误: {e}")
            return None
    
    def save_credentials(self, credentials):
        """保存凭证到文件"""
        try:
            with open(CREDENTIALS_FILE, 'w', encoding='utf-8') as f:
                json.dump(credentials, f, indent=2, ensure_ascii=False)
            return True
        except Exception as e:
            print(f"保存凭证失败: {e}")
            return False
    
    def load_credentials(self):
        """从文件加载凭证"""
        try:
            if os.path.exists(CREDENTIALS_FILE):
                with open(CREDENTIALS_FILE, 'r', encoding='utf-8') as f:
                    return json.load(f)
        except Exception as e:
            print(f"加载凭证失败: {e}")
        return None
    
    def is_credentials_valid(self, credentials):
        """检查凭证是否有效"""
        if not credentials:
            return False
        
        required_keys = ["access_key_id", "access_key_secret", "security_token"]
        if not all(key in credentials for key in required_keys):
            return False
        
        # 检查是否过期（如果有过期时间）
        if "expiration" in credentials and credentials["expiration"] != "手动输入":
            try:
                # 尝试解析过期时间
                expiration = datetime.fromisoformat(credentials["expiration"].replace('Z', '+00:00'))
                if datetime.now() >= expiration - timedelta(minutes=5):  # 提前5分钟认为过期
                    return False
            except Exception:
                # 如果无法解析过期时间，认为有效（手动输入的情况）
                pass
        
        return True
    
    def refresh_credentials(self, config_id=None):
        """刷新凭证"""
        credentials = self.get_credentials_from_api(config_id)
        if credentials:
            if self.save_credentials(credentials):
                return credentials
        return None
    
    def get_valid_credentials(self):
        """获取有效凭证（自动刷新）"""
        # 先尝试加载现有凭证
        credentials = self.load_credentials()
        
        # 如果凭证有效，直接返回
        if self.is_credentials_valid(credentials):
            return credentials
        
        # 否则刷新凭证
        return self.refresh_credentials()

# 全局实例
credential_manager = CredentialManager()

# 兼容性函数（保持与原代码的兼容性）
def load_credentials():
    """兼容性函数：加载凭证"""
    return credential_manager.load_credentials()

def save_credentials(credentials):
    """兼容性函数：保存凭证"""
    return credential_manager.save_credentials(credentials)

def is_credentials_valid(credentials):
    """兼容性函数：检查凭证有效性"""
    return credential_manager.is_credentials_valid(credentials)

def refresh_and_save_credentials(config_id=None):
    """兼容性函数：刷新并保存凭证"""
    return credential_manager.refresh_credentials(config_id)

if __name__ == "__main__":
    # 测试功能
    print("测试凭证管理器...")
    
    # 获取凭证
    creds = credential_manager.get_valid_credentials()
    if creds:
        print("成功获取凭证")
        print(f"来源: {creds.get('source', '未知')}")
        print(f"过期时间: {creds.get('expiration', '未知')}")
    else:
        print("获取凭证失败")
