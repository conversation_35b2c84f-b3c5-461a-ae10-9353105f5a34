"""
测试AIPPT API的脚本
"""
import requests
import json

def test_aippt_token_api():
    """测试AIPPT获取上传凭证的API"""
    
    url = "https://www.aippt.cn/api/upload/oss/token"
    
    # 测试数据
    data = {
        "format": "pdf",
        "upload_type": "other"
    }
    
    # 尝试不同的请求头组合
    headers_list = [
        # 基本请求头
        {
            'Content-Type': 'application/json',
            'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36'
        },
        
        # 完整浏览器请求头
        {
            'Content-Type': 'application/json',
            'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36',
            'Accept': 'application/json, text/plain, */*',
            'Accept-Language': 'zh-CN,zh;q=0.9,en;q=0.8',
            'Origin': 'https://www.aippt.cn',
            'Referer': 'https://www.aippt.cn/'
        },
        
        # 带认证的请求头（可能需要登录）
        {
            'Content-Type': 'application/json',
            'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36',
            'Accept': 'application/json, text/plain, */*',
            'Accept-Language': 'zh-CN,zh;q=0.9,en;q=0.8',
            'Origin': 'https://www.aippt.cn',
            'Referer': 'https://www.aippt.cn/',
            'Authorization': 'Bearer YOUR_TOKEN_HERE',  # 可能需要登录token
            'X-Requested-With': 'XMLHttpRequest'
        }
    ]
    
    print("=== 测试AIPPT上传凭证API ===")
    print(f"URL: {url}")
    print(f"数据: {json.dumps(data, indent=2)}")
    print()
    
    for i, headers in enumerate(headers_list, 1):
        print(f"--- 测试 #{i} ---")
        print(f"请求头: {json.dumps(headers, indent=2)}")
        
        try:
            response = requests.post(url, json=data, headers=headers, timeout=10)
            
            print(f"状态码: {response.status_code}")
            print(f"响应头: {dict(response.headers)}")
            print(f"响应内容: {response.text}")
            
            if response.status_code == 200:
                try:
                    result = response.json()
                    if result.get("code") == 0:
                        print("✅ 成功获取凭证!")
                        return result
                    else:
                        print(f"❌ API返回错误: {result.get('msg', '未知错误')}")
                except json.JSONDecodeError:
                    print("❌ 响应不是有效的JSON格式")
            else:
                print(f"❌ HTTP错误: {response.status_code}")
                
        except requests.exceptions.RequestException as e:
            print(f"❌ 请求异常: {e}")
        
        print()
    
    print("所有测试完成，未能成功获取凭证")
    return None

def test_different_formats():
    """测试不同的格式和类型组合"""
    
    url = "https://www.aippt.cn/api/upload/oss/token"
    
    # 测试不同的组合
    test_cases = [
        {"format": "pdf", "upload_type": "other"},
        {"format": "jpg", "upload_type": "image"},
        {"format": "png", "upload_type": "image"},
        {"format": "doc", "upload_type": "document"},
        {"format": "ppt", "upload_type": "document"},
    ]
    
    headers = {
        'Content-Type': 'application/json',
        'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36',
        'Accept': 'application/json, text/plain, */*',
        'Accept-Language': 'zh-CN,zh;q=0.9,en;q=0.8',
        'Origin': 'https://www.aippt.cn',
        'Referer': 'https://www.aippt.cn/'
    }
    
    print("=== 测试不同格式和类型 ===")
    
    for i, data in enumerate(test_cases, 1):
        print(f"--- 测试 #{i}: {data} ---")
        
        try:
            response = requests.post(url, json=data, headers=headers, timeout=10)
            print(f"状态码: {response.status_code}")
            print(f"响应: {response.text[:200]}...")  # 只显示前200字符
            
            if response.status_code == 200:
                try:
                    result = response.json()
                    if result.get("code") == 0:
                        print("✅ 成功!")
                    else:
                        print(f"❌ 错误: {result.get('msg', '未知')}")
                except:
                    print("❌ JSON解析失败")
            
        except Exception as e:
            print(f"❌ 异常: {e}")
        
        print()

if __name__ == "__main__":
    # 首先测试基本API
    result = test_aippt_token_api()
    
    if not result:
        print("\n" + "="*50)
        print("基本测试失败，尝试不同的格式组合...")
        test_different_formats()
    
    print("\n" + "="*50)
    print("测试完成!")
    print("\n可能的问题:")
    print("1. 需要登录认证 - API可能需要用户登录后的token")
    print("2. 需要特定的请求头 - 可能有反爬虫机制")
    print("3. IP限制 - 可能只允许特定IP访问")
    print("4. 参数错误 - 可能需要其他必需参数")
    print("5. API地址变更 - 接口地址可能已经改变")
