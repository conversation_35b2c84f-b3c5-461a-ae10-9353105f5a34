"""
AIPPT OSS直传上传工具
使用 https://www.aippt.cn/api/upload/oss/token 接口获取上传凭证
"""
import os
import json
import tkinter as tk
from tkinter import ttk, filedialog, messagebox
from datetime import datetime
import threading
import requests
import base64
from urllib.parse import urlencode

# 配置文件路径
SCRIPT_DIR = os.path.dirname(os.path.abspath(__file__))
SETTINGS_FILE = os.path.join(SCRIPT_DIR, "aippt_settings.json")
HISTORY_FILE = os.path.join(SCRIPT_DIR, "aippt_history.json")

# API配置
TOKEN_API_URL = "https://www.aippt.cn/api/upload/oss/token"

class AipptOSSUploader:
    def __init__(self, root):
        self.root = root
        self.root.title("AIPPT OSS上传工具")
        self.root.geometry("700x500")
        
        # 状态变量
        self.is_uploading = False
        self.upload_token = None
        
        # 配置变量
        self.upload_type_var = tk.StringVar(value="other")
        self.format_var = tk.StringVar(value="pdf")
        
        # 界面变量
        self.file_path_var = tk.StringVar()
        self.result_url_var = tk.StringVar(value="上传后的URL将显示在这里")
        self.status_var = tk.StringVar(value="就绪")
        
        self.history_data = []
        
        self._create_ui()
        self._load_settings()
        self._load_history()
    
    def _create_ui(self):
        """创建用户界面"""
        main_frame = ttk.Frame(self.root, padding="10")
        main_frame.pack(fill=tk.BOTH, expand=True)
        
        # 上传配置
        config_frame = ttk.LabelFrame(main_frame, text="上传配置")
        config_frame.pack(fill=tk.X, pady=5)
        
        row1 = ttk.Frame(config_frame)
        row1.pack(fill=tk.X, padx=5, pady=5)
        
        ttk.Label(row1, text="上传类型:").pack(side=tk.LEFT)
        type_combo = ttk.Combobox(row1, textvariable=self.upload_type_var, width=15, state="readonly")
        type_combo['values'] = ('other', 'image', 'document', 'video')
        type_combo.pack(side=tk.LEFT, padx=5)
        
        ttk.Label(row1, text="文件格式:").pack(side=tk.LEFT, padx=(20,0))
        format_combo = ttk.Combobox(row1, textvariable=self.format_var, width=15, state="readonly")
        format_combo['values'] = ('pdf', 'jpg', 'png', 'gif', 'doc', 'docx', 'ppt', 'pptx', 'mp4', 'avi')
        format_combo.pack(side=tk.LEFT, padx=5)
        
        ttk.Button(row1, text="获取上传凭证", command=self._get_upload_token).pack(side=tk.LEFT, padx=20)
        
        # 凭证状态
        self.token_status_var = tk.StringVar(value="凭证状态: 未获取")
        ttk.Label(row1, textvariable=self.token_status_var).pack(side=tk.RIGHT)
        
        # 文件上传
        upload_frame = ttk.LabelFrame(main_frame, text="文件上传")
        upload_frame.pack(fill=tk.X, pady=5)
        
        file_frame = ttk.Frame(upload_frame)
        file_frame.pack(fill=tk.X, padx=5, pady=5)
        ttk.Entry(file_frame, textvariable=self.file_path_var).pack(side=tk.LEFT, fill=tk.X, expand=True)
        ttk.Button(file_frame, text="选择文件", command=self._browse_file).pack(side=tk.LEFT, padx=5)
        self.upload_btn = ttk.Button(file_frame, text="上传", command=self._upload_file)
        self.upload_btn.pack(side=tk.LEFT, padx=5)
        
        # 状态显示
        status_frame = ttk.Frame(upload_frame)
        status_frame.pack(fill=tk.X, padx=5, pady=2)
        ttk.Label(status_frame, text="状态:").pack(side=tk.LEFT)
        ttk.Label(status_frame, textvariable=self.status_var).pack(side=tk.LEFT, padx=5)
        
        # 上传结果
        result_frame = ttk.LabelFrame(main_frame, text="上传结果")
        result_frame.pack(fill=tk.X, pady=5)
        
        url_frame = ttk.Frame(result_frame)
        url_frame.pack(fill=tk.X, padx=5, pady=5)
        ttk.Entry(url_frame, textvariable=self.result_url_var).pack(side=tk.LEFT, fill=tk.X, expand=True)
        ttk.Button(url_frame, text="复制URL", command=self._copy_url).pack(side=tk.LEFT, padx=5)
        
        # 凭证信息显示
        token_frame = ttk.LabelFrame(main_frame, text="当前凭证信息")
        token_frame.pack(fill=tk.X, pady=5)
        
        self.token_text = tk.Text(token_frame, height=6, wrap=tk.WORD)
        token_scrollbar = ttk.Scrollbar(token_frame, orient="vertical", command=self.token_text.yview)
        self.token_text.configure(yscrollcommand=token_scrollbar.set)
        
        self.token_text.pack(side=tk.LEFT, fill=tk.BOTH, expand=True, padx=5, pady=5)
        token_scrollbar.pack(side=tk.RIGHT, fill=tk.Y)
        
        # 历史记录
        history_frame = ttk.LabelFrame(main_frame, text="上传历史")
        history_frame.pack(fill=tk.BOTH, expand=True, pady=5)
        
        self.history_tree = ttk.Treeview(history_frame, columns=("date", "file", "url"), show="headings", height=6)
        self.history_tree.heading("date", text="时间")
        self.history_tree.heading("file", text="文件")
        self.history_tree.heading("url", text="URL")
        self.history_tree.column("date", width=120)
        self.history_tree.column("file", width=150)
        self.history_tree.column("url", width=300)
        
        history_scrollbar = ttk.Scrollbar(history_frame, orient="vertical", command=self.history_tree.yview)
        self.history_tree.configure(yscrollcommand=history_scrollbar.set)
        
        self.history_tree.pack(side=tk.LEFT, fill=tk.BOTH, expand=True)
        history_scrollbar.pack(side=tk.RIGHT, fill=tk.Y)
        
        self.history_tree.bind("<Double-1>", self._copy_history_url)
    
    def _get_upload_token(self):
        """获取上传凭证"""
        self.status_var.set("正在获取上传凭证...")
        threading.Thread(target=self._do_get_token, daemon=True).start()
    
    def _do_get_token(self):
        """执行获取凭证"""
        try:
            # 准备请求数据
            data = {
                "format": self.format_var.get(),
                "upload_type": self.upload_type_var.get()
            }

            # 添加常见的请求头
            headers = {
                'Content-Type': 'application/json',
                'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36',
                'Accept': 'application/json, text/plain, */*',
                'Accept-Language': 'zh-CN,zh;q=0.9,en;q=0.8',
                'Origin': 'https://www.aippt.cn',
                'Referer': 'https://www.aippt.cn/'
            }

            print(f"请求URL: {TOKEN_API_URL}")
            print(f"请求数据: {data}")
            print(f"请求头: {headers}")

            # 发送请求
            response = requests.post(TOKEN_API_URL, json=data, headers=headers, timeout=10)

            print(f"响应状态码: {response.status_code}")
            print(f"响应头: {dict(response.headers)}")
            print(f"响应内容: {response.text}")

            if response.status_code == 200:
                result = response.json()

                if result.get("code") == 0 and "data" in result:
                    self.upload_token = result["data"]
                    self.token_status_var.set("凭证状态: 已获取")
                    self.status_var.set("上传凭证获取成功")

                    # 显示凭证信息
                    self._display_token_info()

                else:
                    self.token_status_var.set("凭证状态: 获取失败")
                    self.status_var.set(f"获取凭证失败: {result.get('msg', '未知错误')}")
            else:
                self.token_status_var.set("凭证状态: 获取失败")
                self.status_var.set(f"HTTP错误: {response.status_code} - {response.text}")

        except Exception as e:
            self.token_status_var.set("凭证状态: 获取失败")
            self.status_var.set(f"获取凭证失败: {str(e)}")
            print(f"异常详情: {e}")
            import traceback
            traceback.print_exc()
    
    def _display_token_info(self):
        """显示凭证信息"""
        if not self.upload_token:
            return
        
        self.token_text.delete(1.0, tk.END)
        
        info = f"""上传凭证信息:
AccessID: {self.upload_token.get('accessid', 'N/A')}
Host: {self.upload_token.get('host', 'N/A')}
CDN Host: {self.upload_token.get('cdn_host', 'N/A')}
Dir: {self.upload_token.get('dir', 'N/A')}
Expire: {self.upload_token.get('expire', 'N/A')}
Policy: {self.upload_token.get('policy', 'N/A')[:50]}...
Signature: {self.upload_token.get('signature', 'N/A')}
Callback: {self.upload_token.get('callback', 'N/A')[:50]}..."""
        
        self.token_text.insert(tk.END, info)
    
    def _browse_file(self):
        """选择文件"""
        file_path = filedialog.askopenfilename()
        if file_path:
            self.file_path_var.set(file_path)
    
    def _upload_file(self):
        """上传文件"""
        if self.is_uploading:
            return
        
        file_path = self.file_path_var.get()
        if not file_path or not os.path.exists(file_path):
            messagebox.showerror("错误", "请选择有效的文件")
            return
        
        if not self.upload_token:
            messagebox.showerror("错误", "请先获取上传凭证")
            return
        
        self.is_uploading = True
        self.upload_btn.config(state=tk.DISABLED)
        threading.Thread(target=self._do_upload, args=(file_path,), daemon=True).start()
    
    def _do_upload(self, file_path):
        """执行上传"""
        try:
            self.status_var.set("上传中...")
            
            # 准备上传数据
            upload_data = {
                'key': self.upload_token['dir'],
                'policy': self.upload_token['policy'],
                'OSSAccessKeyId': self.upload_token['accessid'],
                'signature': self.upload_token['signature'],
                'callback': self.upload_token['callback']
            }
            
            # 准备文件
            with open(file_path, 'rb') as f:
                files = {'file': (os.path.basename(file_path), f, 'application/octet-stream')}
                
                # 上传到OSS
                response = requests.post(
                    self.upload_token['host'],
                    data=upload_data,
                    files=files,
                    timeout=60
                )
            
            if response.status_code == 200:
                # 构建访问URL
                cdn_host = self.upload_token.get('cdn_host', self.upload_token['host'])
                file_url = f"{cdn_host}/{self.upload_token['dir']}"
                
                self.result_url_var.set(file_url)
                self.status_var.set("上传成功")
                
                # 复制到剪贴板
                self.root.clipboard_clear()
                self.root.clipboard_append(file_url)
                
                # 添加到历史
                self._add_to_history(os.path.basename(file_path), file_url)
                
                messagebox.showinfo("成功", "文件上传成功，URL已复制到剪贴板")
                
            else:
                self.status_var.set(f"上传失败: HTTP {response.status_code}")
                messagebox.showerror("上传失败", f"HTTP {response.status_code}: {response.text}")
                
        except Exception as e:
            self.status_var.set(f"上传失败: {str(e)}")
            messagebox.showerror("上传失败", str(e))
        finally:
            self.is_uploading = False
            self.upload_btn.config(state=tk.NORMAL)
    
    def _copy_url(self):
        """复制URL"""
        url = self.result_url_var.get()
        if url and url != "上传后的URL将显示在这里":
            self.root.clipboard_clear()
            self.root.clipboard_append(url)
            self.status_var.set("URL已复制")
    
    def _copy_history_url(self, event):
        """复制历史URL"""
        selection = self.history_tree.selection()
        if selection:
            item = self.history_tree.item(selection[0])
            url = item['values'][2]
            self.root.clipboard_clear()
            self.root.clipboard_append(url)
            self.status_var.set("历史URL已复制")
    
    def _add_to_history(self, filename, url):
        """添加到历史"""
        now = datetime.now().strftime("%m-%d %H:%M")
        self.history_data.append({"date": now, "file": filename, "url": url})
        
        # 保留最近50条
        if len(self.history_data) > 50:
            self.history_data = self.history_data[-50:]
        
        self._update_history_display()
        self._save_history()
    
    def _update_history_display(self):
        """更新历史显示"""
        for item in self.history_tree.get_children():
            self.history_tree.delete(item)
        
        for item in reversed(self.history_data):
            self.history_tree.insert("", 0, values=(item["date"], item["file"], item["url"]))
    
    def _save_settings(self):
        """保存设置"""
        settings = {
            "upload_type": self.upload_type_var.get(),
            "format": self.format_var.get()
        }
        try:
            with open(SETTINGS_FILE, 'w', encoding='utf-8') as f:
                json.dump(settings, f, indent=2)
        except Exception as e:
            print(f"保存设置失败: {e}")
    
    def _load_settings(self):
        """加载设置"""
        try:
            if os.path.exists(SETTINGS_FILE):
                with open(SETTINGS_FILE, 'r', encoding='utf-8') as f:
                    settings = json.load(f)
                self.upload_type_var.set(settings.get("upload_type", "other"))
                self.format_var.set(settings.get("format", "pdf"))
        except Exception as e:
            print(f"加载设置失败: {e}")
    
    def _save_history(self):
        """保存历史"""
        try:
            with open(HISTORY_FILE, 'w', encoding='utf-8') as f:
                json.dump(self.history_data, f, indent=2)
        except Exception as e:
            print(f"保存历史失败: {e}")
    
    def _load_history(self):
        """加载历史"""
        try:
            if os.path.exists(HISTORY_FILE):
                with open(HISTORY_FILE, 'r', encoding='utf-8') as f:
                    self.history_data = json.load(f)
                self._update_history_display()
        except Exception as e:
            print(f"加载历史失败: {e}")
    
    def on_closing(self):
        """关闭时保存设置"""
        self._save_settings()
        self.root.destroy()

if __name__ == "__main__":
    root = tk.Tk()
    app = AipptOSSUploader(root)
    root.protocol("WM_DELETE_WINDOW", app.on_closing)
    root.mainloop()
