import requests
import random
import json
import os
import time

# 脚本目录
SCRIPT_DIR = os.path.dirname(os.path.abspath(__file__))
# 请求记录文件路径
REQUEST_LOG_FILE = os.path.join(SCRIPT_DIR, "request_log.json")

# API 端点 URL
url = "https://bot.n.cn/api/v1/aliy/assumerole"

# 多个请求头配置
HEADERS_CONFIGS = [
    # 配置1 - 原始配置
    {
        "access-token": "48239925286233875932760370017484",
        "device-platform": "Web",   
        "origin": "https://bot.n.cn",
        "referer": "https://bot.n.cn/?src=dh_bj",
        "timestamp": "2025-05-28T17:08:20+08:00",
        "user-agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********",
        "zm-token": "76f6255d73d1d73487297e6a7c8cecbe",
        "zm-ua": "99d149899c4f2f3d79df1f8e73f539ef",
        "zm-ver": "1.2"
    },
    # 配置2
    {
        "access-token": "48239925216757509597943900017484",
        "device-platform": "Web",   
        "origin": "https://bot.n.cn",
        "referer": "https://bot.n.cn/?src=dh_2nd",
        "timestamp": "2025-05-28T17:14:52+08:00",
        "user-agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/136.0.0.0 Safari/537.36",
        "zm-token": "d8feab78effb765425ba672cdba04f1f",
        "zm-ua": "0e0369e2813db7deb26e5937c353aab4",
        "zm-ver": "1.2"
    },
    # 配置3
    {
        "access-token": "25170478133103499604803100017484",
        "device-platform": "Web",   
        "origin": "https://bot.n.cn",
        "referer": "https://bot.n.cn/?src=dh_3rd",
        "timestamp": "2025-05-28T17:24:48+08:00",
        "user-agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Firefox/123.0",
        "zm-token": "8f44b6af76fbe4169cda02009c104f08",
        "zm-ua": "0e0369e2813db7deb26e5937c353aab4",
        "zm-ver": "1.2"
    },
    # 配置4
    {
        "access-token": "23521063131862494451435003001748",
        "device-platform": "Web",   
        "origin": "https://bot.n.cn",
        "referer": "https://bot.n.cn/?src=dh_4th",
        "timestamp": "2025-05-28T17:30:41+08:00",
        "user-agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/137.0.0.0 Safari/537.36",
        "zm-token": "56ee7efb07392d77c6ca8353635154d4",
        "zm-ua": "99d149899c4f2f3d79df1f8e73f539ef",
        "zm-ver": "1.2"
    },
    # 配置5
    {
        "access-token": "25170478148025258802218090017466",
        "device-platform": "Web",   
        "origin": "https://bot.n.cn",
        "referer": "https://bot.n.cn/?src=dh_5th",
        "timestamp": "2025-05-28T16:33:27+08:00",
        "user-agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/134.0.0.0 Safari/537.36 OPR/120.0",
        "zm-token": "e3fb9c7bdbabf11931c205eb42a608ec",
        "zm-ua": "99d149899c4f2f3d79df1f8e73f539c4",
        "zm-ver": "1.2"
    }
]

# 加载请求记录
def load_request_log():
    """加载请求记录，用于跟踪上次使用的配置ID"""
    if os.path.exists(REQUEST_LOG_FILE):
        try:
            with open(REQUEST_LOG_FILE, 'r') as f:
                return json.load(f)
        except Exception:
            pass
    return {"last_config_id": -1, "last_time": 0, "success_count": [0, 0, 0, 0, 0]}

# 保存请求记录
def save_request_log(log_data):
    """保存请求记录"""
    try:
        with open(REQUEST_LOG_FILE, 'w') as f:
            json.dump(log_data, f, indent=2)
    except Exception as e:
        print(f"保存请求记录失败: {e}")

# 选择下一个请求头配置
def select_next_config(request_log=None):
    """
    选择下一个请求头配置
    
    策略:
    1. 默认轮询
    2. 如果传入request_log，则根据成功率选择较成功的配置
    """
    if request_log is None:
        request_log = load_request_log()
    
    last_id = request_log["last_config_id"]
    success_count = request_log["success_count"]
    
    # 简单轮询策略
    next_id = (last_id + 1) % len(HEADERS_CONFIGS)
    
    # 记录本次选择
    request_log["last_config_id"] = next_id
    request_log["last_time"] = int(time.time())
    save_request_log(request_log)
    
    return next_id, HEADERS_CONFIGS[next_id]

# 更新请求成功记录
def update_success_record(config_id, success):
    """更新请求成功记录"""
    request_log = load_request_log()
    if success:
        request_log["success_count"][config_id] += 1
    save_request_log(request_log)

def assume_aliy_role(config_id=None):
    """
    获取阿里云临时凭证
    
    参数:
        config_id: 指定使用的配置ID(0-4)，如果不提供则自动选择
    
    返回:
        成功: 包含凭证信息的字典
        失败: None
    """
    request_log = load_request_log()
    
    # 如果未指定配置ID，则自动选择下一个
    if config_id is None:
        config_id, headers = select_next_config(request_log)
        print(f"自动轮换配置，使用配置 #{config_id+1}")
    else:
        # 验证配置ID有效性
        if config_id < 0 or config_id >= len(HEADERS_CONFIGS):
            print(f"警告: 无效的配置ID: {config_id}，有效范围为0-{len(HEADERS_CONFIGS)-1}，将使用配置 #1")
            config_id = 0
        headers = HEADERS_CONFIGS[config_id]
    
    print(f"使用请求头配置 #{config_id+1}")

    try:
        # 发送 POST 请求，因为 content-length 为 0，所以不传递 data 或 json 参数
        response = requests.post(url, headers=headers)
        
        print(f"响应状态码: {response.status_code}")
        response.raise_for_status() # 如果状态码不是 2xx，则抛出异常

        # 打印响应内容 (JSON格式)
        response_json = response.json()
        print("响应内容 (JSON):")
        print(response_json)

        if response_json.get("code") == 0 and "data" in response_json:
            print("\n成功获取临时凭证:")
            credentials = response_json["data"]
            print(f"  Access Key ID: {credentials.get('access_key_id')}")
            print(f"  Access Key Secret: {credentials.get('access_key_secret')}")
            print(f"  Security Token: {credentials.get('security_token')}")
            print(f"  Expiration: {credentials.get('expiration')}")
            
            # 记录成功
            update_success_record(config_id, True)
            
            return credentials
        else:
            print(f"\n获取凭证失败或响应格式不符合预期: {response_json.get('msg', '未知错误')}")
            update_success_record(config_id, False)
            return None

    except requests.exceptions.HTTPError as http_err:
        print(f"HTTP 错误: {http_err}")
        print(f"响应文本: {response.text}")
    except requests.exceptions.RequestException as req_err:
        print(f"请求发生错误: {req_err}") # 包含了 json decode error
    except Exception as e:
        print(f"发生未知错误: {e}")
    
    # 记录失败
    update_success_record(config_id, False)
    return None

def display_configs():
    """显示所有配置信息"""
    print("可用的请求头配置:")
    for i, config in enumerate(HEADERS_CONFIGS):
        print(f"配置 #{i+1}:")
        print(f"  Referer: {config['referer']}")
        print(f"  User-Agent: {config['user-agent']}")
        print(f"  zm-token: {config['zm-token']}")
    
    # 显示成功率
    request_log = load_request_log()
    success_count = request_log["success_count"]
    print("\n各配置成功次数:")
    for i, count in enumerate(success_count):
        print(f"  配置 #{i+1}: {count} 次")

if __name__ == "__main__":
    import argparse
    
    parser = argparse.ArgumentParser(description="获取阿里云临时凭证")
    parser.add_argument('-c', '--config', type=int, help='指定使用的配置ID (1-5)')
    parser.add_argument('-l', '--list', action='store_true', help='显示所有配置信息')
    
    args = parser.parse_args()
    
    if args.list:
        display_configs()
    else:
        # 如果指定了配置ID，则使用指定的配置（注意转为0-based索引）
        # 确保接收到的config参数(1-5)正确转换为0-4的索引
        config_id = None
        if args.config:
            if 1 <= args.config <= 5:  # 验证配置ID范围是否在1-5之间
                config_id = args.config - 1  # 转换为0-based索引
            else:
                print(f"警告: 无效的配置ID {args.config}，应该在1-5之间。将使用自动轮换。")
        
        assume_aliy_role(config_id)