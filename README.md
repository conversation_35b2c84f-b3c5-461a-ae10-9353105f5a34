# 阿里云OSS上传工具 (alyoss-nami)

这是一个使用阿里云OSS服务的文件上传工具，支持GUI界面操作和命令行使用。工具使用阿里云临时凭证进行身份验证，支持多种请求头配置轮换以提高请求成功率。

## 主要功能

- 图形界面文件上传
- 自动凭证管理与刷新
- 上传失败自动重试（最多3次）
- 多配置轮换提高请求成功率
- 生成20位随机十六进制文件名（如`ebebc0aa03dd88cb1c7f.png`）
- 自动记录上传历史

## 快速开始

### 安装依赖

```bash
pip install oss2 pillow requests
```

### 运行GUI工具

```bash
python aliy_oss_gui.py
```

## 文件结构

- `aliy_oss_gui.py` - GUI主程序
- `aliy_oss_uploader.py` - OSS上传核心功能
- `refresh_credentials.py` - 凭证刷新工具
- `assume_role.py` - 凭证获取工具（多请求头配置）
- `aliy_credentials.json` - 凭证保存文件（自动生成）
- `aliy_upload_history.json` - 上传历史记录文件（自动生成）
- `request_log.json` - 请求记录文件（自动生成）

## 请求头配置说明

工具内置了5种不同的请求头配置，用于轮换请求阿里云临时凭证。每种配置使用不同的标识信息（如User-Agent、Referer等），以降低单一配置被限制的风险。

系统特点：
- **上传失败自动重试**：上传失败时自动刷新凭证并重试，最多3次
- **配置自动轮换**：每次重试时使用不同配置获取新凭证
- **完整日志记录**：记录凭证获取和上传过程的所有关键信息

## 使用指南

### 通过GUI上传文件

1. 启动GUI应用程序
2. 在"文件上传"选项卡中选择文件或拖放文件
3. 点击"上传"按钮开始上传
4. 上传完成后，URL会自动复制到剪贴板
5. 如果上传失败，系统会自动重试（最多3次）

### 设置存储位置

1. 在"存储设置"选项卡中
2. 输入示例OSS URL并点击"解析"
3. 修改存储桶、端点或路径前缀（如需要）
4. 点击"保存设置"

### 查看上传历史

在主界面的历史记录区域可以查看最近的上传记录：
- 双击记录可复制URL
- 历史记录自动保存（最多保留100条）

## 高级功能

### 文件命名

上传文件时默认使用随机20位十六进制字符串作为文件名，例如：
```
ebebc0aa03dd88cb1c7f.png
```

### 请求成功率统计

系统会记录每个请求头配置的成功次数，统计数据保存在request_log.json中。

## 详细运行逻辑

### 上传流程

1. **文件选择**：用户选择要上传的文件
2. **凭证验证**：系统自动检查当前凭证是否有效
   - 如果凭证无效或不存在，自动刷新凭证
3. **文件名生成**：系统使用`secrets`模块生成20位随机十六进制文件名
4. **上传尝试**：系统尝试上传文件到阿里云OSS
   - 如果上传成功，显示URL并复制到剪贴板
   - 如果上传失败，进入重试流程

### 重试机制

1. **首次失败**：记录错误信息，使用配置#1刷新凭证，然后重试
2. **二次失败**：记录错误信息，使用配置#2刷新凭证，然后重试
3. **三次失败**：记录错误信息，显示上传失败消息

### 凭证刷新流程

1. **请求发起**：`refresh_credentials.py`调用`assume_role.py`获取新凭证
2. **配置选择**：根据指定的配置ID选择对应的请求头信息
3. **请求发送**：向阿里云API发送请求获取临时凭证
4. **凭证保存**：将获取的凭证保存到`aliy_credentials.json`文件
5. **统计更新**：更新配置成功率统计，保存到`request_log.json`

### 配置轮换机制

- 系统记录每个配置的使用情况和成功率
- 上传失败时，按照1→2→3→4→5的顺序轮换配置
- 每个配置使用不同的请求头信息，提高请求成功率

## 故障排查

如果遇到上传失败，请查看以下情况：

1. 检查网络连接
2. 查看日志输出信息
3. 确认oss2库已正确安装
4. 如果三次重试均失败，可能需要检查请求配置

## 更新历史

- 1.2.0 - 增强版本
  - 删除凭证管理页面，简化用户操作
  - 上传失败时自动刷新凭证并重试（最多3次）
  - 自动复制上传成功的URL到剪贴板
  - 改进错误处理和日志记录

- 1.1.0 - 更新版本
  - 将所有文件移至alyoss-nami目录
  - 修复文件路径引用问题
  - 改进文件命名为官方风格的20位十六进制格式

- 1.0.0 - 初始版本
  - 基本的文件上传功能
  - 凭证管理和刷新
  - 历史记录管理 