# AIPPT OSS上传工具

这是一个专门用于AIPPT平台的OSS直传上传工具，使用AIPPT的上传接口获取凭证后直接上传到阿里云OSS。

## 与之前项目的区别

### 之前的项目 (simple_oss_uploader.py)
- 使用STS临时凭证方式
- 需要Access Key、Secret Key、Security Token
- 适用于一般的阿里云OSS上传

### 新项目 (aippt_oss_uploader.py)
- 使用OSS直传方式
- 通过AIPPT接口获取policy和signature
- 专门适配AIPPT平台的上传流程

## 上传流程

### 1. 获取上传凭证
```
POST https://www.aippt.cn/api/upload/oss/token
Content-Type: application/json

{
    "format": "pdf",
    "upload_type": "other"
}
```

### 2. 接口返回凭证
```json
{
    "code": 0,
    "data": {
        "accessid": "LTAI5tEC4LwH9eYcd5ANuTnS",
        "host": "https://aippt-domestic.oss-cn-beijing.aliyuncs.com",
        "expire": 0,
        "signature": "ic8qm9Gee6njsgmAerO/eQasjxc=",
        "policy": "eyJleHBpcmF0aW9uIjoi...",
        "dir": "aippt-server/other/doc/51983/10396675/20250603150928yvrspgo.pdf",
        "callback": "eyJjYWxsYmFja1VybCI6...",
        "cdn_host": "https://aippt-domestic.oss-accelerate.aliyuncs.com"
    },
    "msg": "ok"
}
```

### 3. 直传到OSS
使用返回的凭证信息，通过表单POST方式直接上传到OSS：
```
POST {host}
Content-Type: multipart/form-data

key: {dir}
policy: {policy}
OSSAccessKeyId: {accessid}
signature: {signature}
callback: {callback}
file: {文件内容}
```

## 功能特点

### 1. 智能配置
- **上传类型选择**：other、image、document、video
- **文件格式选择**：pdf、jpg、png、gif、doc、docx、ppt、pptx、mp4、avi
- **自动匹配**：根据选择的类型和格式获取对应凭证

### 2. 凭证管理
- **一键获取**：点击"获取上传凭证"按钮
- **状态显示**：实时显示凭证获取状态
- **详细信息**：显示完整的凭证信息供查看

### 3. 文件上传
- **简单操作**：选择文件后点击上传
- **进度提示**：实时显示上传状态
- **自动复制**：上传成功后URL自动复制到剪贴板

### 4. 历史记录
- **记录保存**：自动保存上传历史
- **快速复制**：双击历史记录复制URL
- **数量限制**：最多保存50条记录

## 使用说明

### 安装依赖
```bash
pip install requests
```

### 运行程序
```bash
python aippt_oss_uploader.py
```

### 操作步骤

1. **配置上传参数**
   - 选择上传类型（other、image、document、video）
   - 选择文件格式（pdf、jpg、png等）

2. **获取上传凭证**
   - 点击"获取上传凭证"按钮
   - 等待凭证获取成功

3. **上传文件**
   - 点击"选择文件"选择要上传的文件
   - 点击"上传"开始上传
   - 上传成功后URL会自动复制到剪贴板

## 界面说明

### 上传配置区域
- **上传类型**：选择文件的用途类型
- **文件格式**：选择文件的格式
- **获取上传凭证**：获取当前配置的上传凭证
- **凭证状态**：显示当前凭证状态

### 文件上传区域
- **文件路径**：显示选择的文件路径
- **选择文件**：打开文件选择对话框
- **上传**：开始上传文件
- **状态**：显示当前操作状态

### 上传结果区域
- **URL显示**：显示上传成功后的文件URL
- **复制URL**：手动复制URL到剪贴板

### 当前凭证信息区域
- 显示当前获取的凭证详细信息
- 包括AccessID、Host、Policy等信息

### 上传历史区域
- 显示最近的上传记录
- 双击记录可复制对应的URL

## 技术实现

### 1. 凭证获取
```python
def _do_get_token(self):
    data = {
        "format": self.format_var.get(),
        "upload_type": self.upload_type_var.get()
    }
    response = requests.post(TOKEN_API_URL, json=data, timeout=10)
    result = response.json()
    self.upload_token = result["data"]
```

### 2. 文件上传
```python
def _do_upload(self, file_path):
    upload_data = {
        'key': self.upload_token['dir'],
        'policy': self.upload_token['policy'],
        'OSSAccessKeyId': self.upload_token['accessid'],
        'signature': self.upload_token['signature'],
        'callback': self.upload_token['callback']
    }
    
    with open(file_path, 'rb') as f:
        files = {'file': (os.path.basename(file_path), f, 'application/octet-stream')}
        response = requests.post(self.upload_token['host'], data=upload_data, files=files)
```

### 3. URL构建
```python
cdn_host = self.upload_token.get('cdn_host', self.upload_token['host'])
file_url = f"{cdn_host}/{self.upload_token['dir']}"
```

## 注意事项

1. **凭证有效期**：每次上传前建议重新获取凭证
2. **文件大小**：注意AIPPT平台的文件大小限制
3. **网络连接**：确保网络连接稳定
4. **格式匹配**：确保选择的格式与实际文件格式匹配

## 项目文件

```
📁 项目目录/
├── 📄 aippt_oss_uploader.py     # 🎯 主程序
├── 📄 aippt_settings.json       # ⚙️ 设置文件（自动生成）
├── 📄 aippt_history.json        # 📝 历史记录（自动生成）
└── 📄 README_AIPPT上传工具.md   # 📖 使用说明
```

这个工具专门为AIPPT平台设计，使用简单，功能完整，是AIPPT用户上传文件的理想选择。
