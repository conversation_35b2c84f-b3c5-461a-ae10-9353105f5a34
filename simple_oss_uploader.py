"""
简化版阿里云OSS上传工具 - 单文件版本
整合所有功能到一个文件中，简化使用
"""
import os
import sys
import json
import time
import tkinter as tk
from tkinter import ttk, filedialog, messagebox
from datetime import datetime
import threading
import secrets
import requests
import oss2

# 配置文件路径
SCRIPT_DIR = os.path.dirname(os.path.abspath(__file__))
CREDENTIALS_FILE = os.path.join(SCRIPT_DIR, "credentials.json")
SETTINGS_FILE = os.path.join(SCRIPT_DIR, "settings.json")
HISTORY_FILE = os.path.join(SCRIPT_DIR, "history.json")

# API配置
API_URL = "https://bot.n.cn/api/v1/aliy/assumerole"
API_HEADERS = {
    "access-token": "48239925286233875932760370017484",
    "device-platform": "Web",
    "origin": "https://bot.n.cn",
    "referer": "https://bot.n.cn/?src=dh_bj",
    "timestamp": "2025-05-28T17:08:20+08:00",
    "user-agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36",
    "zm-token": "76f6255d73d1d73487297e6a7c8cecbe",
    "zm-ua": "99d149899c4f2f3d79df1f8e73f539ef",
    "zm-ver": "1.2"
}

class SimpleOSSUploader:
    def __init__(self, root):
        self.root = root
        self.root.title("阿里云OSS上传工具 - 简化版")
        self.root.geometry("700x500")
        
        # 状态变量
        self.is_uploading = False
        self.credentials = None
        
        # 配置变量
        self.bucket_var = tk.StringVar(value="zm-cloud")
        self.endpoint_var = tk.StringVar(value="oss-cn-beijing.aliyuncs.com")
        self.prefix_var = tk.StringVar(value="aisou/")
        
        # 凭证变量
        self.credential_mode_var = tk.StringVar(value="auto")
        self.access_key_var = tk.StringVar()
        self.secret_key_var = tk.StringVar()
        self.token_var = tk.StringVar()
        
        # 界面变量
        self.file_path_var = tk.StringVar()
        self.result_url_var = tk.StringVar(value="上传后的URL将显示在这里")
        self.status_var = tk.StringVar(value="就绪")
        
        self.history_data = []
        
        self._create_ui()
        self._load_settings()
        self._load_credentials()
        self._load_history()
    
    def _create_ui(self):
        """创建用户界面"""
        main_frame = ttk.Frame(self.root, padding="10")
        main_frame.pack(fill=tk.BOTH, expand=True)
        
        # OSS配置
        config_frame = ttk.LabelFrame(main_frame, text="OSS配置")
        config_frame.pack(fill=tk.X, pady=5)
        
        row1 = ttk.Frame(config_frame)
        row1.pack(fill=tk.X, padx=5, pady=5)
        ttk.Label(row1, text="存储桶:").pack(side=tk.LEFT)
        ttk.Entry(row1, textvariable=self.bucket_var, width=15).pack(side=tk.LEFT, padx=5)
        ttk.Label(row1, text="端点:").pack(side=tk.LEFT, padx=(10,0))
        ttk.Entry(row1, textvariable=self.endpoint_var, width=25).pack(side=tk.LEFT, padx=5)
        ttk.Label(row1, text="前缀:").pack(side=tk.LEFT, padx=(10,0))
        ttk.Entry(row1, textvariable=self.prefix_var, width=10).pack(side=tk.LEFT, padx=5)
        
        # 凭证配置
        cred_frame = ttk.LabelFrame(main_frame, text="凭证配置")
        cred_frame.pack(fill=tk.X, pady=5)
        
        mode_frame = ttk.Frame(cred_frame)
        mode_frame.pack(fill=tk.X, padx=5, pady=5)
        ttk.Radiobutton(mode_frame, text="自动获取", variable=self.credential_mode_var, 
                       value="auto", command=self._toggle_credential_mode).pack(side=tk.LEFT)
        ttk.Radiobutton(mode_frame, text="手动输入", variable=self.credential_mode_var, 
                       value="manual", command=self._toggle_credential_mode).pack(side=tk.LEFT, padx=20)
        
        ttk.Button(mode_frame, text="刷新凭证", command=self._refresh_credentials).pack(side=tk.LEFT, padx=20)
        
        self.cred_status_var = tk.StringVar(value="凭证状态: 未知")
        ttk.Label(mode_frame, textvariable=self.cred_status_var).pack(side=tk.RIGHT)
        
        # 手动凭证输入区域
        self.manual_frame = ttk.Frame(cred_frame)
        
        for i, (label, var) in enumerate([("Access Key:", self.access_key_var), 
                                         ("Secret Key:", self.secret_key_var), 
                                         ("Token:", self.token_var)]):
            row = ttk.Frame(self.manual_frame)
            row.pack(fill=tk.X, padx=5, pady=2)
            ttk.Label(row, text=label, width=12).pack(side=tk.LEFT)
            entry = ttk.Entry(row, textvariable=var, width=50)
            if "Key" in label or "Token" in label:
                entry.config(show="*")
            entry.pack(side=tk.LEFT, padx=5)
        
        ttk.Button(self.manual_frame, text="保存凭证", command=self._save_manual_credentials).pack(pady=5)
        
        # 文件上传
        upload_frame = ttk.LabelFrame(main_frame, text="文件上传")
        upload_frame.pack(fill=tk.X, pady=5)
        
        file_frame = ttk.Frame(upload_frame)
        file_frame.pack(fill=tk.X, padx=5, pady=5)
        ttk.Entry(file_frame, textvariable=self.file_path_var).pack(side=tk.LEFT, fill=tk.X, expand=True)
        ttk.Button(file_frame, text="选择", command=self._browse_file).pack(side=tk.LEFT, padx=5)
        self.upload_btn = ttk.Button(file_frame, text="上传", command=self._upload_file)
        self.upload_btn.pack(side=tk.LEFT, padx=5)
        
        # 状态和结果
        status_frame = ttk.Frame(upload_frame)
        status_frame.pack(fill=tk.X, padx=5, pady=2)
        ttk.Label(status_frame, text="状态:").pack(side=tk.LEFT)
        ttk.Label(status_frame, textvariable=self.status_var).pack(side=tk.LEFT, padx=5)
        
        result_frame = ttk.LabelFrame(main_frame, text="上传结果")
        result_frame.pack(fill=tk.X, pady=5)
        
        url_frame = ttk.Frame(result_frame)
        url_frame.pack(fill=tk.X, padx=5, pady=5)
        ttk.Entry(url_frame, textvariable=self.result_url_var).pack(side=tk.LEFT, fill=tk.X, expand=True)
        ttk.Button(url_frame, text="复制", command=self._copy_url).pack(side=tk.LEFT, padx=5)
        
        # 历史记录
        history_frame = ttk.LabelFrame(main_frame, text="上传历史")
        history_frame.pack(fill=tk.BOTH, expand=True, pady=5)
        
        self.history_tree = ttk.Treeview(history_frame, columns=("date", "file", "url"), show="headings", height=6)
        self.history_tree.heading("date", text="时间")
        self.history_tree.heading("file", text="文件")
        self.history_tree.heading("url", text="URL")
        self.history_tree.column("date", width=120)
        self.history_tree.column("file", width=150)
        self.history_tree.column("url", width=300)
        
        scrollbar = ttk.Scrollbar(history_frame, orient="vertical", command=self.history_tree.yview)
        self.history_tree.configure(yscrollcommand=scrollbar.set)
        
        self.history_tree.pack(side=tk.LEFT, fill=tk.BOTH, expand=True)
        scrollbar.pack(side=tk.RIGHT, fill=tk.Y)
        
        self.history_tree.bind("<Double-1>", self._copy_history_url)
    
    def _toggle_credential_mode(self):
        """切换凭证模式"""
        if self.credential_mode_var.get() == "manual":
            self.manual_frame.pack(fill=tk.X, padx=5, pady=5)
        else:
            self.manual_frame.pack_forget()
    
    def _refresh_credentials(self):
        """刷新凭证"""
        if self.credential_mode_var.get() == "auto":
            self.status_var.set("正在获取凭证...")
            threading.Thread(target=self._get_auto_credentials, daemon=True).start()
        else:
            self._save_manual_credentials()
    
    def _get_auto_credentials(self):
        """自动获取凭证"""
        try:
            response = requests.post(API_URL, headers=API_HEADERS, timeout=10)
            data = response.json()
            
            if data.get("code") == 0 and "data" in data:
                creds = data["data"]
                self.credentials = {
                    "access_key_id": creds.get("access_key_id"),
                    "access_key_secret": creds.get("access_key_secret"),
                    "security_token": creds.get("security_token"),
                    "source": "自动获取"
                }
                self._save_credentials()
                self.cred_status_var.set("凭证状态: 有效 (自动获取)")
                self.status_var.set("凭证获取成功")
            else:
                self.cred_status_var.set("凭证状态: 获取失败")
                self.status_var.set("凭证获取失败")
        except Exception as e:
            self.cred_status_var.set("凭证状态: 获取失败")
            self.status_var.set(f"凭证获取失败: {str(e)}")
    
    def _save_manual_credentials(self):
        """保存手动凭证"""
        if not all([self.access_key_var.get(), self.secret_key_var.get(), self.token_var.get()]):
            messagebox.showerror("错误", "请填写完整的凭证信息")
            return
        
        self.credentials = {
            "access_key_id": self.access_key_var.get(),
            "access_key_secret": self.secret_key_var.get(),
            "security_token": self.token_var.get(),
            "source": "手动输入"
        }
        self._save_credentials()
        self.cred_status_var.set("凭证状态: 有效 (手动输入)")
        messagebox.showinfo("成功", "凭证已保存")
    
    def _browse_file(self):
        """选择文件"""
        file_path = filedialog.askopenfilename()
        if file_path:
            self.file_path_var.set(file_path)
    
    def _upload_file(self):
        """上传文件"""
        if self.is_uploading:
            return
        
        file_path = self.file_path_var.get()
        if not file_path or not os.path.exists(file_path):
            messagebox.showerror("错误", "请选择有效的文件")
            return
        
        if not self.credentials:
            messagebox.showerror("错误", "请先配置凭证")
            return
        
        self.is_uploading = True
        self.upload_btn.config(state=tk.DISABLED)
        threading.Thread(target=self._do_upload, args=(file_path,), daemon=True).start()
    
    def _do_upload(self, file_path):
        """执行上传"""
        try:
            self.status_var.set("上传中...")
            
            # 生成20位十六进制文件名
            filename = os.path.basename(file_path)
            file_ext = os.path.splitext(filename)[1]
            hex_name = secrets.token_hex(10)  # 生成20位十六进制
            
            prefix = self.prefix_var.get()
            if not prefix.endswith('/'):
                prefix += '/'
            object_name = f"{prefix}{hex_name}{file_ext}"
            
            # 创建OSS客户端
            auth = oss2.StsAuth(
                self.credentials["access_key_id"],
                self.credentials["access_key_secret"],
                self.credentials["security_token"]
            )
            bucket = oss2.Bucket(auth, self.endpoint_var.get(), self.bucket_var.get())
            
            # 上传文件
            bucket.put_object_from_file(object_name, file_path)
            
            # 生成URL
            url = f"https://{self.bucket_var.get()}.{self.endpoint_var.get()}/{object_name}"
            
            self.result_url_var.set(url)
            self.status_var.set("上传成功")
            
            # 复制到剪贴板
            self.root.clipboard_clear()
            self.root.clipboard_append(url)
            
            # 添加到历史
            self._add_to_history(filename, url)
            
        except Exception as e:
            self.status_var.set(f"上传失败: {str(e)}")
            messagebox.showerror("上传失败", str(e))
        finally:
            self.is_uploading = False
            self.upload_btn.config(state=tk.NORMAL)
    
    def _copy_url(self):
        """复制URL"""
        url = self.result_url_var.get()
        if url and url != "上传后的URL将显示在这里":
            self.root.clipboard_clear()
            self.root.clipboard_append(url)
            self.status_var.set("URL已复制")
    
    def _copy_history_url(self, event):
        """复制历史URL"""
        selection = self.history_tree.selection()
        if selection:
            item = self.history_tree.item(selection[0])
            url = item['values'][2]
            self.root.clipboard_clear()
            self.root.clipboard_append(url)
            self.status_var.set("历史URL已复制")
    
    def _add_to_history(self, filename, url):
        """添加到历史"""
        now = datetime.now().strftime("%m-%d %H:%M")
        self.history_data.append({"date": now, "file": filename, "url": url})
        
        # 保留最近50条
        if len(self.history_data) > 50:
            self.history_data = self.history_data[-50:]
        
        self._update_history_display()
        self._save_history()
    
    def _update_history_display(self):
        """更新历史显示"""
        for item in self.history_tree.get_children():
            self.history_tree.delete(item)
        
        for item in reversed(self.history_data):
            self.history_tree.insert("", 0, values=(item["date"], item["file"], item["url"]))
    
    def _save_credentials(self):
        """保存凭证"""
        try:
            with open(CREDENTIALS_FILE, 'w', encoding='utf-8') as f:
                json.dump(self.credentials, f, indent=2)
        except Exception as e:
            print(f"保存凭证失败: {e}")
    
    def _load_credentials(self):
        """加载凭证"""
        try:
            if os.path.exists(CREDENTIALS_FILE):
                with open(CREDENTIALS_FILE, 'r', encoding='utf-8') as f:
                    self.credentials = json.load(f)
                    source = self.credentials.get("source", "未知")
                    self.cred_status_var.set(f"凭证状态: 有效 ({source})")
                    
                    # 如果是手动输入的凭证，填充到界面
                    if source == "手动输入":
                        self.credential_mode_var.set("manual")
                        self.access_key_var.set(self.credentials.get("access_key_id", ""))
                        self.secret_key_var.set(self.credentials.get("access_key_secret", ""))
                        self.token_var.set(self.credentials.get("security_token", ""))
                        self._toggle_credential_mode()
        except Exception as e:
            print(f"加载凭证失败: {e}")
    
    def _save_settings(self):
        """保存设置"""
        settings = {
            "bucket": self.bucket_var.get(),
            "endpoint": self.endpoint_var.get(),
            "prefix": self.prefix_var.get(),
            "credential_mode": self.credential_mode_var.get()
        }
        try:
            with open(SETTINGS_FILE, 'w', encoding='utf-8') as f:
                json.dump(settings, f, indent=2)
        except Exception as e:
            print(f"保存设置失败: {e}")
    
    def _load_settings(self):
        """加载设置"""
        try:
            if os.path.exists(SETTINGS_FILE):
                with open(SETTINGS_FILE, 'r', encoding='utf-8') as f:
                    settings = json.load(f)
                self.bucket_var.set(settings.get("bucket", "zm-cloud"))
                self.endpoint_var.set(settings.get("endpoint", "oss-cn-beijing.aliyuncs.com"))
                self.prefix_var.set(settings.get("prefix", "aisou/"))
                self.credential_mode_var.set(settings.get("credential_mode", "auto"))
                self._toggle_credential_mode()
        except Exception as e:
            print(f"加载设置失败: {e}")
    
    def _save_history(self):
        """保存历史"""
        try:
            with open(HISTORY_FILE, 'w', encoding='utf-8') as f:
                json.dump(self.history_data, f, indent=2)
        except Exception as e:
            print(f"保存历史失败: {e}")
    
    def _load_history(self):
        """加载历史"""
        try:
            if os.path.exists(HISTORY_FILE):
                with open(HISTORY_FILE, 'r', encoding='utf-8') as f:
                    self.history_data = json.load(f)
                self._update_history_display()
        except Exception as e:
            print(f"加载历史失败: {e}")
    
    def on_closing(self):
        """关闭时保存设置"""
        self._save_settings()
        self.root.destroy()

if __name__ == "__main__":
    root = tk.Tk()
    app = SimpleOSSUploader(root)
    root.protocol("WM_DELETE_WINDOW", app.on_closing)
    root.mainloop()
